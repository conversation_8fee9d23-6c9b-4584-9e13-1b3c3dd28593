#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Simple Anomaly Detection for Tension and Vibration Data

import sys
import pandas as pd
import numpy as np
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                               QHBoxLayout, QWidget, QPushButton, QLabel,
                               QCheckBox, QGroupBox, QGridLayout, QTextEdit,
                               QSplitter, QSpinBox, QComboBox, QSlider)
from PySide6.QtCore import Qt
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from matplotlib.patches import Circle
from matplotlib.widgets import RectangleSelector
from scipy import stats

class MatplotlibCanvas(FigureCanvas):
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)

class AnomalyDetector(QMainWindow):
    def __init__(self):
        super().__init__()
        self.data = None
        self.time_data = None
        self.anomalies = {}
        self.sampling_rate = 1  # 采样率，1表示显示所有数据点

        # Mouse interaction attributes
        self.crosshair_v = None
        self.crosshair_h1 = None
        self.crosshair_h2 = None
        self.mouse_connected = False
        self.ax1 = None  # Tension plot axis
        self.ax2 = None  # Vibration plot axis

        self.init_ui()
        self.load_default_data()
        
    def init_ui(self):
        self.setWindowTitle('张力与振动异常检测')
        self.setGeometry(100, 100, 1600, 1000)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create splitter
        splitter = QSplitter(Qt.Horizontal)
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(splitter)
        
        # Left: Control panel and chart
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Control panel
        control_panel = self.create_control_panel()
        left_layout.addWidget(control_panel)
        
        # Chart
        self.combined_canvas = MatplotlibCanvas(self, width=12, height=10)
        left_layout.addWidget(self.combined_canvas)
        
        # Right: Anomaly detection results
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Results title
        result_label = QLabel("异常检测结果")
        result_label.setStyleSheet("font-size: 14px; font-weight: bold; color: red;")
        right_layout.addWidget(result_label)
        
        # Results text box
        self.result_text = QTextEdit()
        self.result_text.setMaximumWidth(400)
        self.result_text.setStyleSheet("font-family: monospace; font-size: 10px;")
        right_layout.addWidget(self.result_text)
        
        # Add to splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([1200, 400])
        
    def create_control_panel(self):
        control_group = QGroupBox("控制面板")
        layout = QGridLayout(control_group)
        
        # Sensor selection
        sensor_label = QLabel("选择传感器:")
        layout.addWidget(sensor_label, 0, 0)

        self.sensor_checkboxes = {}
        sensors = ['WT1', 'WT2', 'WT3', 'WT4', 'WT5', 'WT6']

        for i, sensor in enumerate(sensors):
            checkbox = QCheckBox(sensor)
            checkbox.setChecked(i < 3)
            checkbox.stateChanged.connect(self.update_analysis)
            self.sensor_checkboxes[sensor] = checkbox
            layout.addWidget(checkbox, 0, i + 1)

        # Axis selection
        axis_label = QLabel("选择轴向:")
        layout.addWidget(axis_label, 1, 0)

        self.axis_checkboxes = {}
        axes = ['X', 'Y', 'Z']
        for i, axis in enumerate(axes):
            checkbox = QCheckBox(axis)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_analysis)
            self.axis_checkboxes[axis] = checkbox
            layout.addWidget(checkbox, 1, i + 1)

        # Detection method
        method_label = QLabel("检测方法:")
        layout.addWidget(method_label, 2, 0)

        self.method_combo = QComboBox()
        self.method_combo.addItems([
            "Z-Score (标准差)",
            "IQR (四分位距)",
            "张力-振动相关性",
            "综合检测"
        ])
        self.method_combo.currentTextChanged.connect(self.update_analysis)
        layout.addWidget(self.method_combo, 2, 1, 1, 2)
        
        # Sensitivity setting
        sensitivity_label = QLabel("敏感度:")
        layout.addWidget(sensitivity_label, 2, 3)

        self.sensitivity_spin = QSpinBox()
        self.sensitivity_spin.setRange(1, 5)
        self.sensitivity_spin.setValue(3)
        self.sensitivity_spin.valueChanged.connect(self.update_analysis)
        layout.addWidget(self.sensitivity_spin, 2, 4)

        # Data sampling control for large datasets
        sampling_label = QLabel("数据采样:")
        layout.addWidget(sampling_label, 3, 0)

        self.sampling_spin = QSpinBox()
        self.sampling_spin.setRange(1, 100)
        self.sampling_spin.setValue(1)
        self.sampling_spin.setSuffix(" (1=全部数据)")
        self.sampling_spin.valueChanged.connect(self.update_sampling)
        layout.addWidget(self.sampling_spin, 3, 1)

        # Auto-adjust sampling for large datasets
        self.auto_sampling_checkbox = QCheckBox("大数据自动调整")
        self.auto_sampling_checkbox.setChecked(True)
        self.auto_sampling_checkbox.stateChanged.connect(self.update_analysis)
        layout.addWidget(self.auto_sampling_checkbox, 3, 2, 1, 2)
        
        # Update button
        update_button = QPushButton("更新分析")
        update_button.clicked.connect(self.update_analysis)
        layout.addWidget(update_button, 4, 0)

        # Export button
        export_button = QPushButton("导出异常")
        export_button.clicked.connect(self.export_anomalies)
        layout.addWidget(export_button, 4, 1)

        # Reset zoom button
        reset_zoom_button = QPushButton("重置缩放")
        reset_zoom_button.clicked.connect(self.reset_zoom)
        layout.addWidget(reset_zoom_button, 4, 2)

        # Info label
        info_label = QLabel("💡 � = 张力异常, 🔴 = 振动异常 | 使用鼠标滚轮缩放")
        info_label.setStyleSheet("color: blue; font-style: italic; font-size: 11px;")
        layout.addWidget(info_label, 4, 3, 1, 3)
        
        return control_group
    
    def load_default_data(self):
        """Load default CSV file"""
        try:
            self.data = pd.read_csv("/Users/<USER>/Desktop/5口井的完整数据/截/华200-3-14（2点-4点含振动）.csv")
            self.time_data = np.arange(len(self.data))
            self.auto_adjust_sampling()
            self.update_analysis()
        except Exception as e:
            print(f"Cannot load default file: {str(e)}")

    def auto_adjust_sampling(self):
        """Auto-adjust sampling rate based on data size"""
        if self.data is None or not self.auto_sampling_checkbox.isChecked():
            return

        data_size = len(self.data)
        if data_size > 10000:
            suggested_sampling = max(1, data_size // 5000)
            self.sampling_spin.setValue(suggested_sampling)
            self.sampling_rate = suggested_sampling
        else:
            self.sampling_spin.setValue(1)
            self.sampling_rate = 1

    def update_sampling(self):
        """Update sampling rate"""
        self.sampling_rate = self.sampling_spin.value()
        self.update_analysis()

    def reset_zoom(self):
        """Reset zoom to show all data"""
        if hasattr(self, 'combined_canvas') and self.combined_canvas.fig.axes:
            for ax in self.combined_canvas.fig.axes:
                ax.autoscale()
            self.combined_canvas.draw()

    def setup_mouse_interaction(self):
        """Setup mouse interaction functionality"""
        if not self.mouse_connected:
            self.combined_canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
            self.combined_canvas.mpl_connect('button_press_event', self.on_mouse_click)
            self.combined_canvas.mpl_connect('scroll_event', self.on_mouse_scroll)
            self.combined_canvas.mpl_connect('key_press_event', self.on_key_press)
            # Make canvas focusable for keyboard events
            self.combined_canvas.setFocusPolicy(Qt.StrongFocus)
            self.mouse_connected = True

    def on_mouse_move(self, event):
        """Mouse move event handler for crosshair"""
        if event.inaxes is None:
            return

        # Update crosshair positions
        if self.crosshair_v is not None:
            for line in self.crosshair_v:
                line.set_xdata([event.xdata, event.xdata])

        if event.inaxes == self.ax1 and self.crosshair_h1 is not None:
            self.crosshair_h1.set_ydata([event.ydata, event.ydata])
        elif event.inaxes == self.ax2 and self.crosshair_h2 is not None:
            self.crosshair_h2.set_ydata([event.ydata, event.ydata])

        self.combined_canvas.draw_idle()

    def on_mouse_click(self, event):
        """Mouse click event handler for data inspection"""
        if event.inaxes is None:
            return

        # Output data information at click position
        x_pos = int(event.xdata) if event.xdata is not None else 0
        if 0 <= x_pos < len(self.data):
            print(f"\n📍 时间点: {x_pos} 秒")
            if 'Tension' in self.data.columns:
                tension_val = self.data.iloc[x_pos]['Tension']
                print(f"🔧 张力: {tension_val:.2f} N")

            # Show selected sensor vibration data
            selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items()
                              if checkbox.isChecked()]
            selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                            if checkbox.isChecked()]

            print("📊 振动数据:")
            for sensor in selected_sensors:
                for axis in selected_axes:
                    column_name = f"{sensor}_{axis}"
                    if column_name in self.data.columns:
                        value = self.data.iloc[x_pos][column_name]
                        print(f"   {sensor}_{axis}: {value:.4f} Hz")
            print("=" * 40)

    def on_mouse_scroll(self, event):
        """Mouse scroll event handler for zooming"""
        if event.inaxes is None:
            return

        # Get current axis limits
        ax = event.inaxes
        xlim = ax.get_xlim()
        ylim = ax.get_ylim()

        # Improved zoom factors - smaller steps for smoother zooming
        if event.step > 0:  # Scroll up = zoom in
            zoom_factor_x = 0.8  # Zoom in more aggressively on X-axis
            zoom_factor_y = 0.9  # Zoom in less aggressively on Y-axis
        else:  # Scroll down = zoom out
            zoom_factor_x = 1.25  # Zoom out
            zoom_factor_y = 1.1

        # Get mouse position, with fallback to center if outside axis
        if event.xdata is not None and event.ydata is not None:
            x_center = event.xdata
            y_center = event.ydata
        else:
            x_center = (xlim[0] + xlim[1]) / 2
            y_center = (ylim[0] + ylim[1]) / 2

        # Calculate new ranges
        x_range = (xlim[1] - xlim[0]) * zoom_factor_x
        y_range = (ylim[1] - ylim[0]) * zoom_factor_y

        # Calculate new limits with bounds checking
        new_xlim = [x_center - x_range/2, x_center + x_range/2]
        new_ylim = [y_center - y_range/2, y_center + y_range/2]

        # Prevent zooming too far out (beyond original data range)
        if hasattr(self, 'time_data') and self.time_data is not None:
            data_x_min, data_x_max = 0, len(self.time_data) - 1
            if new_xlim[1] - new_xlim[0] > data_x_max - data_x_min:
                # If trying to zoom out beyond data range, limit to data range
                new_xlim = [data_x_min, data_x_max]

        # Prevent zooming too far in (less than 10 data points visible)
        if new_xlim[1] - new_xlim[0] < 10:
            center = (new_xlim[0] + new_xlim[1]) / 2
            new_xlim = [center - 5, center + 5]

        # Apply zoom to current axis
        ax.set_xlim(new_xlim)
        ax.set_ylim(new_ylim)

        # Sync x-axis between plots for time alignment
        if ax == self.ax1 and self.ax2 is not None:
            self.ax2.set_xlim(new_xlim)
        elif ax == self.ax2 and self.ax1 is not None:
            self.ax1.set_xlim(new_xlim)

        # Use draw_idle for better performance
        self.combined_canvas.draw_idle()

    def on_key_press(self, event):
        """Keyboard event handler for additional zoom controls"""
        if event.inaxes is None:
            return

        ax = event.inaxes
        xlim = ax.get_xlim()
        ylim = ax.get_ylim()

        # Get center of current view
        x_center = (xlim[0] + xlim[1]) / 2
        y_center = (ylim[0] + ylim[1]) / 2

        if event.key == '+' or event.key == '=':
            # Zoom in with + key
            zoom_factor_x = 0.8
            zoom_factor_y = 0.9
        elif event.key == '-':
            # Zoom out with - key
            zoom_factor_x = 1.25
            zoom_factor_y = 1.1
        elif event.key == 'r':
            # Reset zoom with 'r' key
            self.reset_zoom()
            return
        elif event.key == 'left':
            # Pan left
            pan_amount = (xlim[1] - xlim[0]) * 0.1
            new_xlim = [xlim[0] - pan_amount, xlim[1] - pan_amount]
            ax.set_xlim(new_xlim)
            if ax == self.ax1 and self.ax2 is not None:
                self.ax2.set_xlim(new_xlim)
            elif ax == self.ax2 and self.ax1 is not None:
                self.ax1.set_xlim(new_xlim)
            self.combined_canvas.draw_idle()
            return
        elif event.key == 'right':
            # Pan right
            pan_amount = (xlim[1] - xlim[0]) * 0.1
            new_xlim = [xlim[0] + pan_amount, xlim[1] + pan_amount]
            ax.set_xlim(new_xlim)
            if ax == self.ax1 and self.ax2 is not None:
                self.ax2.set_xlim(new_xlim)
            elif ax == self.ax2 and self.ax1 is not None:
                self.ax1.set_xlim(new_xlim)
            self.combined_canvas.draw_idle()
            return
        else:
            return

        # Apply zoom
        x_range = (xlim[1] - xlim[0]) * zoom_factor_x
        y_range = (ylim[1] - ylim[0]) * zoom_factor_y

        new_xlim = [x_center - x_range/2, x_center + x_range/2]
        new_ylim = [y_center - y_range/2, y_center + y_range/2]

        # Apply bounds checking
        if hasattr(self, 'time_data') and self.time_data is not None:
            data_x_min, data_x_max = 0, len(self.time_data) - 1
            if new_xlim[1] - new_xlim[0] > data_x_max - data_x_min:
                new_xlim = [data_x_min, data_x_max]

        if new_xlim[1] - new_xlim[0] < 10:
            center = (new_xlim[0] + new_xlim[1]) / 2
            new_xlim = [center - 5, center + 5]

        ax.set_xlim(new_xlim)
        ax.set_ylim(new_ylim)

        # Sync x-axis
        if ax == self.ax1 and self.ax2 is not None:
            self.ax2.set_xlim(new_xlim)
        elif ax == self.ax2 and self.ax1 is not None:
            self.ax1.set_xlim(new_xlim)

        self.combined_canvas.draw_idle()
    
    def detect_anomalies_zscore(self, data, threshold=3):
        """Detect anomalies using Z-Score method"""
        z_scores = np.abs(stats.zscore(data))
        return z_scores > threshold
    
    def detect_anomalies_iqr(self, data):
        """Detect anomalies using IQR method"""
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        return (data < lower_bound) | (data > upper_bound)
    
    def detect_tension_vibration_correlation(self):
        """Detect abnormal tension-vibration correlation"""
        if 'Tension' not in self.data.columns:
            return np.array([])
        
        tension = self.data['Tension'].values
        anomalies = np.zeros(len(tension), dtype=bool)
        
        # Get selected vibration data
        selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items() 
                          if checkbox.isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items() 
                        if checkbox.isChecked()]
        
        vibration_data = []
        for sensor in selected_sensors:
            for axis in selected_axes:
                column_name = f"{sensor}_{axis}"
                if column_name in self.data.columns:
                    vibration_data.append(self.data[column_name].values)
        
        if not vibration_data:
            return anomalies
        
        # Calculate RMS of vibration data
        vibration_rms = np.sqrt(np.mean([v**2 for v in vibration_data], axis=0))
        
        # Detect sudden tension changes
        tension_diff = np.abs(np.diff(tension))
        tension_anomalies = tension_diff > np.percentile(tension_diff, 95)
        
        # Detect vibration anomalies
        vibration_anomalies = self.detect_anomalies_iqr(vibration_rms)
        
        # Combine anomalies
        anomalies[1:] = tension_anomalies
        anomalies = anomalies | vibration_anomalies

        return anomalies

    def perform_anomaly_detection(self):
        """Perform anomaly detection"""
        if self.data is None:
            return

        method = self.method_combo.currentText()
        sensitivity = self.sensitivity_spin.value()

        # Adjust threshold based on sensitivity
        threshold_map = {1: 4, 2: 3.5, 3: 3, 4: 2.5, 5: 2}
        threshold = threshold_map[sensitivity]

        self.anomalies = {}

        # Tension anomaly detection
        if 'Tension' in self.data.columns:
            tension_data = self.data['Tension'].values

            if "Z-Score" in method:
                tension_anomalies = self.detect_anomalies_zscore(tension_data, threshold)
            elif "IQR" in method:
                tension_anomalies = self.detect_anomalies_iqr(tension_data)
            elif "Correlation" in method:
                tension_anomalies = self.detect_tension_vibration_correlation()
            else:  # Combined detection
                z_anomalies = self.detect_anomalies_zscore(tension_data, threshold)
                iqr_anomalies = self.detect_anomalies_iqr(tension_data)
                corr_anomalies = self.detect_tension_vibration_correlation()
                tension_anomalies = z_anomalies | iqr_anomalies | corr_anomalies

            self.anomalies['Tension'] = tension_anomalies

        # Vibration anomaly detection
        selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items()
                          if checkbox.isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                        if checkbox.isChecked()]

        for sensor in selected_sensors:
            for axis in selected_axes:
                column_name = f"{sensor}_{axis}"
                if column_name in self.data.columns:
                    vibration_data = self.data[column_name].values

                    if "Z-Score" in method:
                        vibration_anomalies = self.detect_anomalies_zscore(vibration_data, threshold)
                    elif "IQR" in method:
                        vibration_anomalies = self.detect_anomalies_iqr(vibration_data)
                    elif "Correlation" in method:
                        vibration_anomalies = self.detect_tension_vibration_correlation()
                    else:  # Combined detection
                        z_anomalies = self.detect_anomalies_zscore(vibration_data, threshold)
                        iqr_anomalies = self.detect_anomalies_iqr(vibration_data)
                        vibration_anomalies = z_anomalies | iqr_anomalies

                    self.anomalies[column_name] = vibration_anomalies

    def update_analysis(self):
        """Update analysis and charts"""
        if self.data is None:
            return

        # Perform anomaly detection
        self.perform_anomaly_detection()

        # Update chart
        self.update_combined_plot()

        # Update results text
        self.update_result_text()

    def update_result_text(self):
        """Update anomaly detection results text"""
        result_text = "=== 异常检测结果 ===\n\n"

        total_anomalies = 0
        for key, anomalies in self.anomalies.items():
            anomaly_count = np.sum(anomalies)
            total_anomalies += anomaly_count

            if anomaly_count > 0:
                # Translate key names to Chinese
                if key == 'Tension':
                    key_name = '张力'
                else:
                    key_name = key
                result_text += f"{key_name}: {anomaly_count} 个异常\n"

                # Show first 10 anomaly points
                anomaly_indices = np.where(anomalies)[0][:10]
                for idx in anomaly_indices:
                    time_point = idx
                    value = self.data.iloc[idx][key] if key in self.data.columns else 0
                    result_text += f"  时间 {time_point}秒: {value:.4f}\n"

                if len(np.where(anomalies)[0]) > 10:
                    result_text += f"  ... 还有 {len(np.where(anomalies)[0]) - 10} 个异常\n"
                result_text += "\n"

        result_text += f"异常总数: {total_anomalies}\n"
        result_text += f"异常率: {total_anomalies/len(self.data)*100:.2f}%\n\n"

        # Add analysis suggestions
        result_text += "=== 分析建议 ===\n"
        if total_anomalies == 0:
            result_text += "✅ 未检测到明显异常\n"
        elif total_anomalies < len(self.data) * 0.05:
            result_text += "⚠️ 检测到少量异常，需密切监控\n"
        else:
            result_text += "🚨 检测到大量异常，需详细检查\n"

        self.result_text.setText(result_text)

    def get_sampled_data(self):
        """Get sampled data based on current sampling rate"""
        if self.data is None:
            return None, None

        # Apply sampling
        if self.sampling_rate > 1:
            indices = np.arange(0, len(self.data), self.sampling_rate)
            sampled_data = self.data.iloc[indices]
            sampled_time = self.time_data[indices]
        else:
            sampled_data = self.data
            sampled_time = self.time_data

        return sampled_data, sampled_time

    def update_combined_plot(self):
        """Update combined chart with anomaly markers"""
        if self.data is None:
            return

        self.combined_canvas.fig.clear()

        # Get sampled data for plotting
        sampled_data, sampled_time = self.get_sampled_data()
        if sampled_data is None:
            return

        # Create subplots with shared x-axis for synchronized zooming
        self.ax1 = self.combined_canvas.fig.add_subplot(211)  # Tension plot
        self.ax2 = self.combined_canvas.fig.add_subplot(212, sharex=self.ax1)  # Vibration plot

        # Store references for easier access
        ax1 = self.ax1
        ax2 = self.ax2

        # Plot tension curve
        if 'Tension' in sampled_data.columns:
            tension_data = sampled_data['Tension'].values
            ax1.plot(sampled_time, tension_data, 'b-', linewidth=2, label='Tension', alpha=0.8)

            # Mark tension anomalies with improved style
            if 'Tension' in self.anomalies:
                anomaly_indices = np.where(self.anomalies['Tension'])[0]
                if len(anomaly_indices) > 0:
                    # Filter anomaly indices to match sampled data
                    if self.sampling_rate > 1:
                        sampled_indices = np.arange(0, len(self.data), self.sampling_rate)
                        anomaly_mask = np.isin(anomaly_indices, sampled_indices)
                        visible_anomalies = anomaly_indices[anomaly_mask]
                        # Map to sampled time indices
                        visible_time_indices = visible_anomalies // self.sampling_rate
                        visible_time_indices = visible_time_indices[visible_time_indices < len(sampled_time)]
                    else:
                        visible_anomalies = anomaly_indices
                        visible_time_indices = visible_anomalies

                    if len(visible_time_indices) > 0:
                        # Use red circle markers 35px size
                        ax1.scatter(sampled_time[visible_time_indices],
                                   tension_data[visible_time_indices],
                                   color='red', s=35, marker='o',
                                   edgecolor='darkred', linewidth=1,
                                   label=f'Tension Anomalies ({len(anomaly_indices)})',
                                   zorder=5, alpha=0.8)

            ax1.set_ylabel('Tension (N)', fontsize=12, fontweight='bold')
            ax1.set_title('Tension & Vibration Anomaly Detection Analysis', fontsize=14, fontweight='bold', pad=20)
            ax1.grid(True, alpha=0.3)
            ax1.legend(loc='upper right', fontsize=10)

        # Plot vibration curves
        selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items()
                          if checkbox.isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                        if checkbox.isChecked()]

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F']
        line_styles = ['-', '--', ':']

        plot_count = 0
        total_vibration_anomalies = 0

        for i, sensor in enumerate(selected_sensors):
            for j, axis in enumerate(selected_axes):
                column_name = f"{sensor}_{axis}"
                if column_name in sampled_data.columns:
                    vibration_data = sampled_data[column_name].values
                    color = colors[i % len(colors)]
                    style = line_styles[j % len(line_styles)]

                    # Plot vibration curve
                    ax2.plot(sampled_time, vibration_data,
                             color=color, linestyle=style, linewidth=1.5,
                             label=f"{sensor}_{axis}", alpha=0.7)

                    # Mark vibration anomalies with improved style
                    if column_name in self.anomalies:
                        anomaly_indices = np.where(self.anomalies[column_name])[0]
                        if len(anomaly_indices) > 0:
                            # Filter anomaly indices to match sampled data
                            if self.sampling_rate > 1:
                                sampled_indices = np.arange(0, len(self.data), self.sampling_rate)
                                anomaly_mask = np.isin(anomaly_indices, sampled_indices)
                                visible_anomalies = anomaly_indices[anomaly_mask]
                                visible_time_indices = visible_anomalies // self.sampling_rate
                                visible_time_indices = visible_time_indices[visible_time_indices < len(sampled_time)]
                            else:
                                visible_anomalies = anomaly_indices
                                visible_time_indices = visible_anomalies

                            if len(visible_time_indices) > 0:
                                # Use red circle markers 35px size
                                ax2.scatter(sampled_time[visible_time_indices],
                                           vibration_data[visible_time_indices],
                                           color='red', s=35, marker='o',
                                           edgecolor='darkred', linewidth=1,
                                           zorder=5, alpha=0.8)

                            total_vibration_anomalies += len(anomaly_indices)

                    plot_count += 1

        if plot_count > 0:
            ax2.set_xlabel('Time (seconds)', fontsize=12, fontweight='bold')
            ax2.set_ylabel('Vibration Amplitude (Hz)', fontsize=12, fontweight='bold')
            ax2.grid(True, alpha=0.3)

            # Add sampling info and total anomaly count
            info_text = f'Total Vibration Anomalies: {total_vibration_anomalies}'
            if self.sampling_rate > 1:
                info_text += f'\nSampling: 1/{self.sampling_rate} (showing {len(sampled_data)}/{len(self.data)} points)'
                info_text += f'\nData reduction: {(1-len(sampled_data)/len(self.data))*100:.1f}%'

            if total_vibration_anomalies > 0 or self.sampling_rate > 1:
                ax2.text(0.02, 0.98, info_text,
                        transform=ax2.transAxes, fontsize=9,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8),
                        verticalalignment='top')

            ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)

        # Add crosshair lines for mouse interaction
        self.crosshair_v = []
        # Vertical crosshair (shared across both plots)
        if self.ax1 is not None:
            v_line1 = self.ax1.axvline(color='gray', linestyle='--', alpha=0.7, linewidth=1)
            self.crosshair_v.append(v_line1)
        if self.ax2 is not None:
            v_line2 = self.ax2.axvline(color='gray', linestyle='--', alpha=0.7, linewidth=1)
            self.crosshair_v.append(v_line2)

        # Horizontal crosshairs (separate for each plot)
        if self.ax1 is not None:
            self.crosshair_h1 = self.ax1.axhline(color='gray', linestyle='--', alpha=0.7, linewidth=1)
        if self.ax2 is not None:
            self.crosshair_h2 = self.ax2.axhline(color='gray', linestyle='--', alpha=0.7, linewidth=1)

        # Adjust layout
        self.combined_canvas.fig.tight_layout()
        self.combined_canvas.draw()

        # Setup mouse interaction
        self.setup_mouse_interaction()

    def export_anomalies(self):
        """Export anomaly data"""
        if not self.anomalies:
            print("未检测到异常")
            return

        # Create anomaly dataframe
        anomaly_data = []

        for key, anomalies in self.anomalies.items():
            anomaly_indices = np.where(anomalies)[0]
            for idx in anomaly_indices:
                # Translate key names to Chinese
                if key == 'Tension':
                    data_type = '张力'
                else:
                    data_type = key

                anomaly_data.append({
                    '时间点': idx,
                    '数据类型': data_type,
                    '数值': self.data.iloc[idx][key] if key in self.data.columns else 0,
                    '检测方法': self.method_combo.currentText()
                })

        if anomaly_data:
            anomaly_df = pd.DataFrame(anomaly_data)
            filename = f"异常检测结果.csv"
            anomaly_df.to_csv(filename, index=False)
            print(f"异常数据已导出到: {filename}")
            print(f"导出异常总数: {len(anomaly_data)}")
        else:
            print("无异常数据可导出")

def main():
    app = QApplication(sys.argv)

    window = AnomalyDetector()
    window.show()

    sys.exit(app.exec())

if __name__ == '__main__':
    main()
