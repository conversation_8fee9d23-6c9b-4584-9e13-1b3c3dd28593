#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Combined Tension and Vibration Analyzer with Interactive Crosshair

# 2
import sys
import pandas as pd
import numpy as np
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                               QHBoxLayout, QWidget, QPushButton, QLabel,
                               QCheckBox, QGroupBox, QGridLayout)
from PySide6.QtCore import Qt
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from datetime import datetime, timedelta

class MatplotlibCanvas(FigureCanvas):
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)

class CombinedAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.data = None
        self.time_data = None
        self.crosshair_v = None
        self.crosshair_h1 = None
        self.crosshair_h2 = None
        self.mouse_connected = False
        self.init_ui()
        self.load_default_data()
        
    def init_ui(self):
        self.setWindowTitle('Combined Tension & Vibration Analyzer')
        self.setGeometry(100, 100, 1400, 900)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Control panel
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # Combined chart
        self.combined_canvas = MatplotlibCanvas(self, width=14, height=10)
        main_layout.addWidget(self.combined_canvas)
        
    def create_control_panel(self):
        control_group = QGroupBox("Control Panel")
        layout = QGridLayout(control_group)
        
        # Sensor selection
        sensor_label = QLabel("Select Sensors:")
        layout.addWidget(sensor_label, 0, 0)
        
        self.sensor_checkboxes = {}
        sensors = ['WT1', 'WT2', 'WT3', 'WT4', 'WT5', 'WT6']
        
        for i, sensor in enumerate(sensors):
            checkbox = QCheckBox(sensor)
            checkbox.setChecked(i < 3)  # Default: select first 3 sensors
            checkbox.stateChanged.connect(self.update_combined_plot)
            self.sensor_checkboxes[sensor] = checkbox
            layout.addWidget(checkbox, 0, i + 1)
        
        # Axis selection
        axis_label = QLabel("Select Axes:")
        layout.addWidget(axis_label, 1, 0)
        
        self.axis_checkboxes = {}
        axes = ['X', 'Y', 'Z']
        for i, axis in enumerate(axes):
            checkbox = QCheckBox(axis)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_combined_plot)
            self.axis_checkboxes[axis] = checkbox
            layout.addWidget(checkbox, 1, i + 1)
        
        # Update button
        update_button = QPushButton("Update Charts")
        update_button.clicked.connect(self.update_combined_plot)
        layout.addWidget(update_button, 2, 0)
        
        # Info label
        info_label = QLabel("💡 Tip: Move mouse over charts to see crosshair, click to get data values")
        info_label.setStyleSheet("color: blue; font-style: italic;")
        layout.addWidget(info_label, 2, 1, 1, 6)
        
        return control_group
    
    def load_default_data(self):
        """Load default CSV file"""
        try:
            self.data = pd.read_csv("/Users/<USER>/Desktop/5口井的完整数据/截/138x_8_3_interpolated.csv")
            
            # Process time data - create relative time (seconds)
            self.time_data = np.arange(len(self.data))
            
            self.update_combined_plot()
        except Exception as e:
            print(f"Cannot load default file: {str(e)}")
    
    def setup_mouse_interaction(self):
        """Setup mouse interaction functionality"""
        if not self.mouse_connected:
            self.combined_canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
            self.combined_canvas.mpl_connect('button_press_event', self.on_mouse_click)
            self.mouse_connected = True
    
    def on_mouse_move(self, event):
        """Mouse move event handler"""
        if event.inaxes is None:
            return
        
        # Update crosshair positions
        if self.crosshair_v is not None:
            for line in self.crosshair_v:
                line.set_xdata([event.xdata, event.xdata])
        
        if event.inaxes == self.ax1 and self.crosshair_h1 is not None:
            self.crosshair_h1.set_ydata([event.ydata, event.ydata])
        elif event.inaxes == self.ax2 and self.crosshair_h2 is not None:
            self.crosshair_h2.set_ydata([event.ydata, event.ydata])
        
        self.combined_canvas.draw_idle()
    
    def on_mouse_click(self, event):
        """Mouse click event handler"""
        if event.inaxes is None:
            return
        
        # Output data information at click position
        x_pos = int(event.xdata) if event.xdata is not None else 0
        if 0 <= x_pos < len(self.data):
            print(f"\n📍 Time point: {x_pos} seconds")
            if 'Tension' in self.data.columns:
                tension_val = self.data.iloc[x_pos]['Tension']
                print(f"🔧 Tension: {tension_val:.2f} N")
            
            # Show selected sensor vibration data
            selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items() 
                              if checkbox.isChecked()]
            selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items() 
                            if checkbox.isChecked()]
            
            print("📊 Vibration Data:")
            for sensor in selected_sensors:
                for axis in selected_axes:
                    column_name = f"{sensor}_{axis}"
                    if column_name in self.data.columns:
                        value = self.data.iloc[x_pos][column_name]
                        print(f"   {sensor}_{axis}: {value:.4f} g")
            print("=" * 40)
    
    def update_combined_plot(self):
        """Update combined chart (tension and vibration together)"""
        if self.data is None:
            return
            
        self.combined_canvas.fig.clear()
        
        # Create subplots: top for tension, bottom for vibration
        self.ax1 = self.combined_canvas.fig.add_subplot(211)  # Tension plot
        self.ax2 = self.combined_canvas.fig.add_subplot(212, sharex=self.ax1)  # Vibration plot, shared x-axis
        
        # Plot tension curve
        if 'Tension' in self.data.columns:
            tension_data = self.data['Tension'].values
            self.ax1.plot(self.time_data, tension_data, 'r-', linewidth=2.5, label='Tension', alpha=0.8)
            self.ax1.set_ylabel('Tension (N)', fontsize=12, fontweight='bold')
            self.ax1.set_title('Combined Tension & Vibration Analysis', fontsize=16, fontweight='bold', pad=20)
            self.ax1.grid(True, alpha=0.3)
            self.ax1.legend(loc='upper right', fontsize=10)
            
            # Add tension statistics
            mean_tension = np.mean(tension_data)
            max_tension = np.max(tension_data)
            min_tension = np.min(tension_data)
            self.ax1.axhline(y=mean_tension, color='red', linestyle=':', alpha=0.5, label=f'Mean: {mean_tension:.1f}N')
        
        # Plot vibration curves
        selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items() 
                          if checkbox.isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items() 
                        if checkbox.isChecked()]
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F']
        line_styles = ['-', '--', ':']
        
        plot_count = 0
        for i, sensor in enumerate(selected_sensors):
            for j, axis in enumerate(selected_axes):
                column_name = f"{sensor}_{axis}"
                if column_name in self.data.columns:
                    vibration_data = self.data[column_name].values
                    color = colors[i % len(colors)]
                    style = line_styles[j % len(line_styles)]
                    
                    self.ax2.plot(self.time_data, vibration_data,
                                 color=color, linestyle=style, linewidth=1.8,
                                 label=f"{sensor}_{axis}", alpha=0.8)
                    plot_count += 1
        
        if plot_count > 0:
            self.ax2.set_xlabel('Time (seconds)', fontsize=12, fontweight='bold')
            self.ax2.set_ylabel('Vibration Amplitude (g)', fontsize=12, fontweight='bold')
            self.ax2.grid(True, alpha=0.3)
            self.ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
        
        # Add crosshair lines  添加十字准线
        self.crosshair_v = []
        # Vertical crosshair (shared across both plots)
        v_line1 = self.ax1.axvline(color='gray', linestyle='--', alpha=0.7, linewidth=1)  # 添加水平辅助线
        v_line2 = self.ax2.axvline(color='gray', linestyle='--', alpha=0.7, linewidth=1)
        self.crosshair_v = [v_line1, v_line2]
        
        # Horizontal crosshairs (separate for each plot)
        self.crosshair_h1 = self.ax1.axhline(color='gray', linestyle='--', alpha=0.7, linewidth=1) # 添加垂直辅助线
        self.crosshair_h2 = self.ax2.axhline(color='gray', linestyle='--', alpha=0.7, linewidth=1)
        
        # Adjust layout
        self.combined_canvas.fig.tight_layout()
        self.combined_canvas.draw()
        
        # Setup mouse interaction
        self.setup_mouse_interaction()

def main():
    app = QApplication(sys.argv)
    
    window = CombinedAnalyzer()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
